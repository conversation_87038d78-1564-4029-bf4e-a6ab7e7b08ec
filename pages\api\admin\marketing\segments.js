import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for admin marketing segment management
 * This endpoint uses service_role key to bypass RLS policies
 * Uses the simplified authentication approach
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Authenticate request using our simplified auth module
  const { authorized, error, user, role } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  // Check if the request method is allowed
  if (!['GET', 'POST', 'PUT', 'DELETE'].includes(req.method)) {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // GET - Fetch segments
    if (req.method === 'GET') {
      // Generate a unique request ID for tracking
      const requestId = Math.random().toString(36).substring(2, 8);
      console.log(`[${requestId}] Fetching marketing segments`);

      try {
        // Get query parameters
        const {
          search,
          sort_by = 'created_at',
          sort_order = 'desc',
          limit = 10,
          offset = 0,
        } = req.query;

        // In development mode, we can use mock data for testing
        if (process.env.NODE_ENV === 'development' && process.env.USE_MOCK_DATA === 'true') {
          console.log(`[${requestId}] Using mock segments data in development mode`);

          // Mock data for development testing
          const mockSegments = [
            {
              id: '1',
              name: 'New Customers',
              description: 'Customers who registered in the last 30 days',
              segment_query: { groups: [] },
              created_at: '2023-01-01T00:00:00Z',
              created_by: user.id
            },
            {
              id: '2',
              name: 'High Value Customers',
              description: 'Customers who spent over $500',
              segment_query: { groups: [] },
              created_at: '2023-02-01T00:00:00Z',
              created_by: user.id
            },
            {
              id: '3',
              name: 'Inactive Customers',
              description: 'Customers with no purchases in 90 days',
              segment_query: { groups: [] },
              created_at: '2023-03-01T00:00:00Z',
              created_by: user.id
            }
          ];

          const mockTotal = mockSegments.length;
          const mockPage = Math.floor(Number(offset) / Number(limit)) + 1;

          return res.status(200).json({
            segments: mockSegments,
            total: mockTotal,
            page: mockPage,
            limit: Number(limit),
          });
        }

        // Get admin client with timeout protection
        const timeoutPromise = new Promise((_, reject) => {
          const id = setTimeout(() => {
            clearTimeout(id);
            reject(new Error('Database connection timed out'));
          }, 5000);
        });

        let timeoutId;        timeoutPromise.catch(() => {
          console.error(`[${requestId}] Database connection timed out`);
        });

        // Get admin client
        const adminClient = getAdminClient();

        if (!adminClient) {
          console.error(`[${requestId}] Admin client not available.`);
          return res.status(500).json({
            error: 'Database connection failed',
            requestId
          });
        }

        console.log(`[${requestId}] Admin client obtained successfully`);

        // Start building the query - use customer_segments table instead of segments
        let query = adminClient
          .from('customer_segments')
          .select('*', { count: 'exact' });

        // Apply search filter if provided
        if (search) {
          query = query.or(`name.ilike.%${search}%, description.ilike.%${search}%`);
        }

        // Apply sorting
        query = query.order(sort_by, { ascending: sort_order === 'asc' });

        // Apply pagination
        query = query.range(Number(offset), Number(offset) + Number(limit) - 1);

        // Execute the query
        const { data: segments, error, count } = await query;

        if (error) {
          console.error(`[${requestId}] Error fetching segments:`, error);
          throw error;
        }

        console.log(`[${requestId}] Successfully fetched ${segments?.length || 0} segments`);
        return res.status(200).json({
          segments,
          total: count,
          page: Math.floor(Number(offset) / Number(limit)) + 1,
          limit: Number(limit),
        });
      } catch (error) {
        console.error(`[${requestId}] Error in GET segments:`, error);
        throw error; // Will be caught by the outer try/catch
      }
    }

    // POST - Create new segment
    if (req.method === 'POST') {
      // Generate a unique request ID for tracking
      const requestId = Math.random().toString(36).substring(2, 8);
      console.log(`[${requestId}] Creating new segment`);

      try {
        const { name, description, segment_query, is_active } = req.body;

        if (!name) {
          console.warn(`[${requestId}] Missing segment name for creation`);
          return res.status(400).json({
            error: 'Segment name is required',
            requestId
          });
        }

        // In development mode, we can use mock data for testing
        if (process.env.NODE_ENV === 'development' && process.env.USE_MOCK_DATA === 'true') {
          console.log(`[${requestId}] Using mock data for segment creation in development mode`);

          // Mock data for development testing
          const mockSegment = {
            id: Math.random().toString(36).substring(2, 15),
            name,
            description: description || '',
            segment_query: segment_query || {},
            is_active: is_active ?? true,
            created_by: user.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          return res.status(201).json(mockSegment);
        }

        // Get admin client with timeout protection
        const timeoutPromise = new Promise((_, reject) => {
          const id = setTimeout(() => {
            clearTimeout(id);
            reject(new Error('Database connection timed out'));
          }, 5000);
        });        // Get admin client
        const adminClient = await Promise.race([
          getAdminClient(),
          timeoutPromise
        ]);

        if (!adminClient) {
          console.error(`[${requestId}] Admin client not available.`);
          return res.status(500).json({
            error: 'Database connection failed',
            requestId
          });
        }

        console.log(`[${requestId}] Admin client obtained successfully`);

        // Create the segment - use customer_segments table instead of segments
        // Also use segment_query instead of conditions to match the table schema
        const { data, error } = await adminClient
          .from('customer_segments')
          .insert({
            name,
            description,
            segment_query: segment_query || {},
            created_by: user.id
          })
          .select()
          .single();

        if (error) {
          console.error(`[${requestId}] Error creating segment:`, error);
          throw error;
        }

        console.log(`[${requestId}] Segment created successfully: ${name}`);
        return res.status(201).json(data);
      } catch (error) {
        console.error(`[${requestId}] Error in POST segment:`, error);
        throw error; // Will be caught by the outer try/catch
      }
    }

    // PUT - Update a segment
    if (req.method === 'PUT') {
      // Generate a unique request ID for tracking
      const requestId = Math.random().toString(36).substring(2, 8);
      console.log(`[${requestId}] Updating segment`);

      try {
        const { id } = req.query;
        const { name, description, segment_query, is_active } = req.body;

        if (!id) {
          console.warn(`[${requestId}] Missing segment ID for update`);
          return res.status(400).json({
            error: 'Segment ID is required',
            requestId
          });
        }

        // In development mode, we can use mock data for testing
        if (process.env.NODE_ENV === 'development' && process.env.USE_MOCK_DATA === 'true') {
          console.log(`[${requestId}] Using mock data for segment update in development mode`);

          // Mock data for development testing
          const mockSegment = {
            id,
            name: name || 'Updated Segment',
            description: description || 'Updated description',
            segment_query: segment_query || {},
            is_active: is_active ?? true,
            created_by: user.id,
            created_at: '2023-01-01T00:00:00Z',
            updated_at: new Date().toISOString()
          };

          return res.status(200).json(mockSegment);
        }

        const updateData = {
          updated_at: new Date().toISOString()
        };

        if (name !== undefined) updateData.name = name;
        if (description !== undefined) updateData.description = description;
        if (segment_query !== undefined) updateData.segment_query = segment_query;
        if (is_active !== undefined) updateData.is_active = is_active;

        // Get admin client with timeout protection
        const timeoutPromise = new Promise((_, reject) => {
          const id = setTimeout(() => {
            clearTimeout(id);
            reject(new Error('Database connection timed out'));
          }, 5000);
        });

        // Get admin client
        const adminClient = await Promise.race([
          getAdminClient(),        timeoutPromise
        ]);

        if (!adminClient) {
          console.error(`[${requestId}] Admin client not available.`);
          return res.status(500).json({
            error: 'Database connection failed',
            requestId
          });
        }

        console.log(`[${requestId}] Admin client obtained successfully`);

        // Update the segment - use customer_segments table instead of segments
        const { data, error } = await adminClient
          .from('customer_segments')
          .update(updateData)
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error(`[${requestId}] Error updating segment:`, error);
          throw error;
        }

        if (!data) {
          console.warn(`[${requestId}] Segment not found for update: ${id}`);
          return res.status(404).json({
            error: 'Segment not found',
            requestId
          });
        }

        console.log(`[${requestId}] Segment updated successfully: ${id}`);
        return res.status(200).json(data);
      } catch (error) {
        console.error(`[${requestId}] Error in PUT segment:`, error);
        throw error; // Will be caught by the outer try/catch
      }
    }

    // DELETE - Delete a segment
    if (req.method === 'DELETE') {
      // Generate a unique request ID for tracking
      const requestId = Math.random().toString(36).substring(2, 8);
      console.log(`[${requestId}] Deleting segment`);

      try {
        const { id } = req.query;

        if (!id) {
          console.warn(`[${requestId}] Missing segment ID for deletion`);
          return res.status(400).json({
            error: 'Segment ID is required',
            requestId
          });
        }

        // In development mode, we can use mock data for testing
        if (process.env.NODE_ENV === 'development' && process.env.USE_MOCK_DATA === 'true') {
          console.log(`[${requestId}] Using mock data for segment deletion in development mode`);
          return res.status(204).send();
        }

        // Get admin client with timeout protection
        const timeoutPromise = new Promise((_, reject) => {
          const id = setTimeout(() => {
            clearTimeout(id);
            reject(new Error('Database connection timed out'));
          }, 5000);
        });

        // Get admin client
        const adminClient = await Promise.race([
          getAdminClient(),        timeoutPromise
        ]);

        if (!adminClient) {
          console.error(`[${requestId}] Admin client not available.`);
          return res.status(500).json({
            error: 'Database connection failed',
            requestId
          });
        }

        console.log(`[${requestId}] Admin client obtained successfully`);

        // Check if segment exists before deleting
        const { data: existingSegment, error: checkError } = await adminClient
          .from('customer_segments')
          .select('id')
          .eq('id', id)
          .single();

        if (checkError) {
          console.error(`[${requestId}] Error checking segment existence:`, checkError);
          throw checkError;
        }

        if (!existingSegment) {
          console.warn(`[${requestId}] Segment not found for deletion: ${id}`);
          return res.status(404).json({
            error: 'Segment not found',
            requestId
          });
        }

        // Delete the segment - use customer_segments table instead of segments
        const { error } = await adminClient
          .from('customer_segments')
          .delete()
          .eq('id', id);

        if (error) {
          console.error(`[${requestId}] Error deleting segment:`, error);
          throw error;
        }

        console.log(`[${requestId}] Segment deleted successfully: ${id}`);
        return res.status(204).send();
      } catch (error) {
        console.error(`[${requestId}] Error in DELETE segment:`, error);
        throw error; // Will be caught by the outer try/catch
      }
    }
  } catch (error) {
    // Generate a unique request ID for tracking if not already generated
    const requestId = Math.random().toString(36).substring(2, 8);
    console.error(`[${requestId}] Error in segments API:`, error);

    // Determine the appropriate status code based on the error
    let statusCode = 500;
    let errorMessage = 'Failed to process segment request';

    if (error.message && error.message.includes('not found')) {
      statusCode = 404; // Not Found
      errorMessage = 'Requested segment not found';
    } else if (error.message && (
      error.message.includes('permission') ||
      error.message.includes('access') ||
      error.message.includes('unauthorized')
    )) {
      statusCode = 403; // Forbidden
      errorMessage = 'Permission denied';
    } else if (error.message && error.message.includes('validation')) {
      statusCode = 400; // Bad Request
      errorMessage = 'Validation error';
    } else if (error.message && error.message.includes('duplicate')) {
      statusCode = 409; // Conflict
      errorMessage = 'Resource already exists';
    } else if (error.message && error.message.includes('timeout')) {
      statusCode = 504; // Gateway Timeout
      errorMessage = 'Request timed out';
    } else if (error.message && error.message.includes('relation') && error.message.includes('does not exist')) {
      statusCode = 500; // Internal Server Error
      errorMessage = 'Database schema error';

      // In development mode, provide more detailed error information about the table issue
      if (process.env.NODE_ENV === 'development') {
        console.log(`[${requestId}] Table does not exist error. Using mock data fallback.`);

        // Return mock data for segments in development mode
        if (req.method === 'GET') {
          const mockSegments = [
            {
              id: '1',
              name: 'New Customers',
              description: 'Customers who registered in the last 30 days',
              segment_query: { groups: [] },
              created_at: '2023-01-01T00:00:00Z'
            },
            {
              id: '2',
              name: 'High Value Customers',
              description: 'Customers who spent over $500',
              segment_query: { groups: [] },
              created_at: '2023-02-01T00:00:00Z'
            }
          ];

          return res.status(200).json({
            segments: mockSegments,
            total: mockSegments.length,
            page: 1,
            limit: 10,
            _dev_note: 'Using mock data due to database schema error'
          });
        }
      }
    }

    // In development mode, provide more detailed error information
    const errorResponse = {
      error: errorMessage,
      message: error.message || 'An error occurred while processing your request',
      timestamp: new Date().toISOString(),
      requestId
    };

    // Add stack trace and code in development mode
    if (process.env.NODE_ENV === 'development') {
      errorResponse.details = error.stack;
      errorResponse.code = error.code;
    }

    return res.status(statusCode).json(errorResponse);
  }
}
