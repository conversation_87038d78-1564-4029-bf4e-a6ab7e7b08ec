.analyticsSection {
  margin-bottom: 30px;
}

.analyticsSection h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.2rem;
  color: #333;
}

.analyticsSection h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1rem;
  color: #333;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.noData {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.metricCard {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.metricCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metricValue {
  font-size: 1.8rem;
  font-weight: 600;
  color: #6e8efb;
  margin-bottom: 8px;
}

.metricLabel {
  font-size: 0.9rem;
  color: #666;
}

.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.chartCard {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chartContainer {
  height: 300px;
  position: relative;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.statCard {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.statCard h4 {
  margin-bottom: 10px;
}

.statValue {
  font-size: 1.5rem;
  font-weight: 600;
  color: #6e8efb;
  margin-bottom: 4px;
}

.statLabel {
  font-size: 0.85rem;
  color: #666;
}

.tableCard {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.tableContainer {
  overflow-x: auto;
}

.dataTable {
  width: 100%;
  border-collapse: collapse;
}

.dataTable th {
  text-align: left;
  padding: 12px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  font-size: 0.9rem;
  color: #333;
}

.dataTable td {
  padding: 12px;
  border-bottom: 1px solid #eee;
  font-size: 0.9rem;
  color: #666;
}

.dataTable tr:last-child td {
  border-bottom: none;
}

.dataTable tr:hover td {
  background-color: rgba(110, 142, 251, 0.05);
}

.periodSelector {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.periodButton {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  color: #666;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.periodButton:hover {
  background-color: #f5f5f5;
}

.periodButtonActive {
  background-color: #6e8efb;
  color: white;
  border-color: #6e8efb;
}

.periodButtonActive:hover {
  background-color: #5a7df9;
}

.dateRangeSelector {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 20px;
}

.dateInput {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.applyButton {
  padding: 8px 16px;
  background-color: #6e8efb;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.applyButton:hover {
  background-color: #5a7df9;
}

@media (max-width: 768px) {
  .chartsGrid {
    grid-template-columns: 1fr;
  }
  
  .chartContainer {
    height: 250px;
  }
  
  .periodSelector {
    flex-wrap: wrap;
  }
  
  .periodButton {
    flex: 1;
    text-align: center;
  }
  
  .dateRangeSelector {
    flex-direction: column;
    align-items: stretch;
  }
}
