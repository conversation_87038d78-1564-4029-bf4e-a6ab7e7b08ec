import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import SalesDashboard from '@/components/admin/analytics/SalesDashboard';
import ReportExporter from '@/components/admin/analytics/ReportExporter';
import styles from '@/styles/admin/Analytics.module.css';

export default function AnalyticsDashboard() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'sales', 'customers', 'bookings'
  const [stats, setStats] = useState({
    totalBookings: 0,
    totalOrders: 0,
    totalRevenue: 0,
    averageOrderValue: 0,
    conversionRate: 0,
    topProducts: [],
    topServices: []
  });
  const [period, setPeriod] = useState('month'); // 'day', 'week', 'month', 'year'
  const [customDateRange, setCustomDateRange] = useState(false);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [timeframe, setTimeframe] = useState('last30days');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch analytics dashboard data
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      setLoading(true)
      setError(null)

      try {
        // Fetch analytics stats
        const response = await fetch(`/api/admin/analytics/stats?timeframe=${timeframe}`)

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch analytics data')
        }

        const data = await response.json()
        setStats(data)
      } catch (error) {
        console.error('Error fetching analytics data:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchAnalyticsData()
  }, [timeframe])

  // Initialize date range
  useEffect(() => {
    const now = new Date();
    const start = new Date(now);
    start.setMonth(start.getMonth() - 1);

    setStartDate(start);
    setEndDate(now);
  }, []);

  // Handle timeframe change
  const handleTimeframeChange = (newTimeframe) => {
    setTimeframe(newTimeframe);
  }

  // Handle period change
  const handlePeriodChange = (newPeriod) => {
    setPeriod(newPeriod);
    setCustomDateRange(false);
  };

  // Handle date range change
  const handleDateRangeChange = (e) => {
    const { name, value } = e.target;

    if (name === 'startDate') {
      setStartDate(new Date(value));
    } else if (name === 'endDate') {
      setEndDate(new Date(value));
    }
  };

  // Format date for input
  const formatDateForInput = (date) => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  // Format currency
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(value);
  }

  // Format percentage
  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  }

  // Render tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <>
            {/* Key Metrics */}
            <section className={styles.metricsGrid}>
              <div className={styles.metricCard}>
                <h3>Total Revenue</h3>
                <p className={styles.metricValue}>{formatCurrency(stats.totalRevenue)}</p>
              </div>
              <div className={styles.metricCard}>
                <h3>Orders</h3>
                <p className={styles.metricValue}>{stats.totalOrders}</p>
              </div>
              <div className={styles.metricCard}>
                <h3>Bookings</h3>
                <p className={styles.metricValue}>{stats.totalBookings}</p>
              </div>
              <div className={styles.metricCard}>
                <h3>Avg. Order Value</h3>
                <p className={styles.metricValue}>{formatCurrency(stats.averageOrderValue)}</p>
              </div>
              <div className={styles.metricCard}>
                <h3>Conversion Rate</h3>
                <p className={styles.metricValue}>{formatPercentage(stats.conversionRate)}</p>
              </div>
            </section>

            {/* Top Products & Services */}
            <div className={styles.columnsContainer}>
              <section className={styles.column}>
                <h2>Top Products</h2>
                <div className={styles.tableContainer}>
                  <table className={styles.dataTable}>
                    <thead>
                      <tr>
                        <th>Product</th>
                        <th>Units Sold</th>
                        <th>Revenue</th>
                      </tr>
                    </thead>
                    <tbody>
                      {stats.topProducts.length > 0 ? (
                        stats.topProducts.map((product) => (
                          <tr key={product.id}>
                            <td>{product.name}</td>
                            <td>{product.unitsSold}</td>
                            <td>{formatCurrency(product.revenue)}</td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="3" className={styles.emptyState}>No product data available</td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </section>

              <section className={styles.column}>
                <h2>Top Services</h2>
                <div className={styles.tableContainer}>
                  <table className={styles.dataTable}>
                    <thead>
                      <tr>
                        <th>Service</th>
                        <th>Bookings</th>
                        <th>Revenue</th>
                      </tr>
                    </thead>
                    <tbody>
                      {stats.topServices.length > 0 ? (
                        stats.topServices.map((service) => (
                          <tr key={service.id}>
                            <td>{service.name}</td>
                            <td>{service.bookings}</td>
                            <td>{formatCurrency(service.revenue)}</td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="3" className={styles.emptyState}>No service data available</td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </section>
            </div>
          </>
        );
      case 'sales':
        return (
          <SalesDashboard
            period={period}
            customDateRange={customDateRange}
            startDate={startDate}
            endDate={endDate}
            refreshKey={refreshKey}
          />
        );
      case 'customers':
        return (
          <div className={styles.comingSoon}>
            <h3>Customer Analytics</h3>
            <p>Customer analytics dashboard is coming soon.</p>
          </div>
        );
      case 'bookings':
        return (
          <div className={styles.comingSoon}>
            <h3>Booking Analytics</h3>
            <p>Booking analytics dashboard is coming soon.</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <ProtectedRoute>
      <AdminLayout title="Analytics Dashboard">
        <div className={styles.analyticsContainer}>
          <header className={styles.header}>
            <h1>Analytics & Reporting</h1>
            <div className={styles.tabsContainer}>
              <button
                className={`${styles.tabButton} ${activeTab === 'overview' ? styles.activeTab : ''}`}
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </button>
              <button
                className={`${styles.tabButton} ${activeTab === 'sales' ? styles.activeTab : ''}`}
                onClick={() => setActiveTab('sales')}
              >
                Sales
              </button>
              <button
                className={`${styles.tabButton} ${activeTab === 'customers' ? styles.activeTab : ''}`}
                onClick={() => setActiveTab('customers')}
              >
                Customers
              </button>
              <button
                className={`${styles.tabButton} ${activeTab === 'bookings' ? styles.activeTab : ''}`}
                onClick={() => setActiveTab('bookings')}
              >
                Bookings
              </button>
            </div>
          </header>

          {activeTab === 'overview' && (
            <div className={styles.timeframeSelector}>
              <button
                className={`${styles.timeframeButton} ${timeframe === 'last7days' ? styles.active : ''}`}
                onClick={() => handleTimeframeChange('last7days')}
              >
                Last 7 Days
              </button>
              <button
                className={`${styles.timeframeButton} ${timeframe === 'last30days' ? styles.active : ''}`}
                onClick={() => handleTimeframeChange('last30days')}
              >
                Last 30 Days
              </button>
              <button
                className={`${styles.timeframeButton} ${timeframe === 'last90days' ? styles.active : ''}`}
                onClick={() => handleTimeframeChange('last90days')}
              >
                Last 90 Days
              </button>
            </div>
          )}

          {activeTab !== 'overview' && (
            <div className={styles.periodControls}>
              <div className={styles.periodButtons}>
                <button
                  className={`${styles.periodButton} ${
                    period === 'day' && !customDateRange ? styles.activePeriod : ''
                  }`}
                  onClick={() => handlePeriodChange('day')}
                >
                  Today
                </button>
                <button
                  className={`${styles.periodButton} ${
                    period === 'week' && !customDateRange ? styles.activePeriod : ''
                  }`}
                  onClick={() => handlePeriodChange('week')}
                >
                  Week
                </button>
                <button
                  className={`${styles.periodButton} ${
                    period === 'month' && !customDateRange ? styles.activePeriod : ''
                  }`}
                  onClick={() => handlePeriodChange('month')}
                >
                  Month
                </button>
                <button
                  className={`${styles.periodButton} ${
                    period === 'year' && !customDateRange ? styles.activePeriod : ''
                  }`}
                  onClick={() => handlePeriodChange('year')}
                >
                  Year
                </button>
                <button
                  className={`${styles.periodButton} ${
                    customDateRange ? styles.activePeriod : ''
                  }`}
                  onClick={() => setCustomDateRange(true)}
                >
                  Custom
                </button>
              </div>

              {customDateRange && (
                <div className={styles.dateRangePicker}>
                  <div className={styles.dateInput}>
                    <label htmlFor="startDate">From:</label>
                    <input
                      type="date"
                      id="startDate"
                      name="startDate"
                      value={formatDateForInput(startDate)}
                      onChange={handleDateRangeChange}
                    />
                  </div>
                  <div className={styles.dateInput}>
                    <label htmlFor="endDate">To:</label>
                    <input
                      type="date"
                      id="endDate"
                      name="endDate"
                      value={formatDateForInput(endDate)}
                      onChange={handleDateRangeChange}
                    />
                  </div>
                  <button
                    className={styles.applyButton}
                    onClick={() => setRefreshKey(prev => prev + 1)}
                  >
                    Apply
                  </button>
                </div>
              )}
            </div>
          )}

          {loading && activeTab === 'overview' ? (
            <div className={styles.loadingContainer}>
              <div className={styles.loader}></div>
              <p>Loading analytics data...</p>
            </div>
          ) : error && activeTab === 'overview' ? (
            <div className={styles.errorContainer}>
              <p className={styles.errorMessage}>{error}</p>
              <button
                className={styles.retryButton}
                onClick={() => fetchAnalyticsData()}
              >
                Retry
              </button>
            </div>
          ) : (
            <div className={styles.contentContainer}>
              {renderTabContent()}
            </div>
          )}

          {activeTab !== 'overview' && (
            <div className={styles.exporterContainer}>
              <ReportExporter
                period={period}
                customDateRange={customDateRange}
                startDate={startDate}
                endDate={endDate}
              />
            </div>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
