import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for inventory statistics
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response with inventory statistics
 */
export default async function handler(req, res) {
  // Authenticate request using our robust auth module
  const { authorized, error } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({ error: 'Unauthorized access' });
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get admin client
    const supabase = getAdminClient();
    if (!supabase) {
      return res.status(500).json({ error: 'Failed to initialize admin client' });
    }

    // Get total products count
    const { count: totalProducts, error: productsError } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    if (productsError) throw productsError;

    // Get active products count
    const { count: activeProducts, error: activeProductsError } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active');

    if (activeProductsError) throw activeProductsError;

    // Get total services count
    const { count: totalServices, error: servicesError } = await supabase
      .from('services')
      .select('*', { count: 'exact', head: true });

    if (servicesError) throw servicesError;

    // Get active services count
    const { count: activeServices, error: activeServicesError } = await supabase
      .from('services')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active');

    if (activeServicesError) throw activeServicesError;

    return res.status(200).json({
      totalProducts,
      activeProducts,
      totalServices,
      activeServices,
    });
  } catch (error) {
    console.error('Inventory stats error:', error);
    return res.status(500).json({
      error: 'Failed to fetch inventory statistics',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}
